﻿using FurryFriends.BlazorUI.Client.Models.PetWalkers;
using FurryFriends.BlazorUI.Client.Services.Interfaces;
using Microsoft.AspNetCore.Components;

namespace FurryFriends.BlazorUI.Client.Pages.PetWalkers;

public partial class ManageSchedulePopup
{
  [Parameter]
  public Guid PetWalkerId { get; set; }

  [Parameter]
  public EventCallback OnSave { get; set; }

  [Parameter]
  public EventCallback OnCancel { get; set; }

  [Inject]
  public IScheduleService ScheduleService { get; set; } = default!;

  [Inject]
  public ILogger<ManageSchedulePopup> Logger { get; set; } = default!;

  private List<ScheduleItemDto> scheduleItems = new();
  private bool isLoading = true;
  private bool isSaving = false;
  private string? errorMessage = null;
  private bool showSuccessMessage = false;

  protected override async Task OnInitializedAsync()
  {
    await LoadSchedule();
  }

  protected override async Task OnParametersSetAsync()
  {
    if (PetWalkerId != Guid.Empty)
    {
      await LoadSchedule();
    }
  }

  private async Task LoadSchedule()
  {
    try
    {
      isLoading = true;
      errorMessage = null;
      showSuccessMessage = false;
      StateHasChanged();

      Logger.LogInformation("Loading schedule for PetWalker: {PetWalkerId}", PetWalkerId);

      var response = await ScheduleService.GetScheduleAsync(PetWalkerId);

      if (response.Success && response.Data != null)
      {
        scheduleItems = response.Data.Schedules.ToList();

        // Ensure we have entries for all days of the week
        foreach (var day in ScheduleHelper.WeekDays)
        {
          if (!scheduleItems.Any(s => s.DayOfWeek == day))
          {
            scheduleItems.Add(new ScheduleItemDto
            {
              DayOfWeek = day,
              StartTime = TimeOnly.FromDateTime(DateTime.Parse("09:00")), // Default 9 AM
              EndTime = TimeOnly.FromDateTime(DateTime.Parse("17:00")),   // Default 5 PM
              IsActive = false
            });
          }
        }

        // Sort by day of week
        scheduleItems = scheduleItems.OrderBy(s => (int)s.DayOfWeek == 0 ? 7 : (int)s.DayOfWeek).ToList();

        Logger.LogInformation("Successfully loaded schedule with {Count} items", scheduleItems.Count);
      }
      else
      {
        errorMessage = response.Message ?? "Failed to load schedule";
        Logger.LogWarning("Failed to load schedule: {Error}", errorMessage);

        // Initialize with empty schedule for all days
        scheduleItems = ScheduleHelper.CreateEmptyWeeklySchedule();
      }
    }
    catch (Exception ex)
    {
      errorMessage = $"An error occurred: {ex.Message}";
      Logger.LogError(ex, "Error loading schedule for PetWalker: {PetWalkerId}", PetWalkerId);

      // Initialize with empty schedule for all days
      scheduleItems = ScheduleHelper.CreateEmptyWeeklySchedule();
    }
    finally
    {
      isLoading = false;
      StateHasChanged();
    }
  }

  private async Task SaveSchedule()
  {
    try
    {
      isSaving = true;
      errorMessage = null;
      showSuccessMessage = false;
      StateHasChanged();

      // Validate all active schedule items
      var activeSchedules = scheduleItems.Where(s => s.IsActive).ToList();
      var invalidSchedules = activeSchedules.Where(s => !ScheduleHelper.IsValidScheduleItem(s)).ToList();

      if (invalidSchedules.Any())
      {
        errorMessage = "Please fix the validation errors before saving.";
        return;
      }

      Logger.LogInformation("Saving schedule for PetWalker: {PetWalkerId} with {ActiveCount} active days",
        PetWalkerId, activeSchedules.Count);

      var response = await ScheduleService.SetScheduleAsync(PetWalkerId, scheduleItems);

      if (response.Success)
      {
        showSuccessMessage = true;
        Logger.LogInformation("Successfully saved schedule for PetWalker: {PetWalkerId}", PetWalkerId);

        StateHasChanged();

        // Auto-close after showing success message
        await Task.Delay(1500);
        await OnSave.InvokeAsync();
      }
      else
      {
        errorMessage = response.Message ?? "Failed to save schedule";
        Logger.LogWarning("Failed to save schedule: {Error}", errorMessage);
      }
    }
    catch (Exception ex)
    {
      errorMessage = $"An error occurred: {ex.Message}";
      Logger.LogError(ex, "Error saving schedule for PetWalker: {PetWalkerId}", PetWalkerId);
    }
    finally
    {
      isSaving = false;
      StateHasChanged();
    }
  }

  private async Task OnCancelClick()
  {
    await OnCancel.InvokeAsync();
  }
}
