@using FurryFriends.BlazorUI.Client.Models.PetWalkers
@using FurryFriends.BlazorUI.Client.Services.Interfaces
@rendermode InteractiveAuto

<div class="modal-backdrop">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header modal-header-background">
				<h5 class="modal-title">Edit <PERSON></h5>
				<button type="button" class="btn-close" aria-label="Close" @onclick="OnCancel"></button>
			</div>
			<div class="modal-body">
				@if (isLoading)
				{
					<div class="loading-container">
						<p><em>Loading pet walker data...</em></p>
					</div>
				}
				else if (loadError != null)
				{
					<div class="alert alert-danger alert-dismissible fade show" role="alert">
						@loadError
						<button type="button" class="btn-close" @onclick="@(() => loadError = null)"
								aria-label="Close"></button>
					</div>
				}
				else if (petWalkerModel != null)
				{
					<EditForm Model="@petWalkerModel" OnValidSubmit="@HandleValidSubmit">
						<DataAnnotationsValidator />
						<ValidationSummary />

						<div class="form-sections">
							<div class="form-section">
								<h4>Personal Information</h4>
								<div class="form-group">
									<label for="name">Name</label>
									<InputText id="name" class="form-control" @bind-Value="petWalkerModel.Name" />
									<ValidationMessage For="@(() => petWalkerModel.Name)" />
								</div>

								<div class="form-group">
									<label>Phone Number</label>
									<div class="input-group">
										<div class="input-group-prepend">
											<span class="input-group-text">+</span>
										</div>
										<InputText class="form-control country-code" style="max-width: 60px;"
												   @bind-Value="petWalkerModel.CountryCode" placeholder="Code" />
										<InputText class="form-control" @bind-Value="petWalkerModel.PhoneNumber"
												   placeholder="Phone Number" />
									</div>
									<ValidationMessage For="@(() => petWalkerModel.CountryCode)" />
									<ValidationMessage For="@(() => petWalkerModel.PhoneNumber)" />
								</div>

								<div class="form-group">
									<label for="gender">Gender</label>
									<InputSelect id="gender" class="form-control" @bind-Value="petWalkerModel.Gender">
										<option value="">Select Gender</option>
										<option value="Male">Male</option>
										<option value="Female">Female</option>
										<option value="Other">Other</option>
									</InputSelect>
									<ValidationMessage For="@(() => petWalkerModel.Gender)" />
								</div>

								<div class="form-group">
									<label for="biography">Biography</label>
									<InputTextArea id="biography" class="form-control"
												   @bind-Value="petWalkerModel.Biography" rows="4" />
									<ValidationMessage For="@(() => petWalkerModel.Biography)" />
								</div>
							</div>

							<div class="form-section">
								<h4>Professional Details</h4>
								<div class="form-group">
									<label for="hourlyRate">Hourly Rate ($)</label>
									<InputNumber id="hourlyRate" class="form-control"
												 @bind-Value="petWalkerModel.HourlyRate" />
									<ValidationMessage For="@(() => petWalkerModel.HourlyRate)" />
								</div>

								<div class="form-group">
									<label for="yearsOfExperience">Years of Experience</label>
									<InputNumber id="yearsOfExperience" class="form-control"
												 @bind-Value="petWalkerModel.YearsOfExperience" />
									<ValidationMessage For="@(() => petWalkerModel.YearsOfExperience)" />
								</div>

								<div class="form-group">
									<label for="dailyPetWalkLimit">Daily Pet Walk Limit</label>
									<InputNumber id="dailyPetWalkLimit" class="form-control"
												 @bind-Value="petWalkerModel.DailyPetWalkLimit" />
									<ValidationMessage For="@(() => petWalkerModel.DailyPetWalkLimit)" />
								</div>

								<div class="form-group">
									<label>Certifications</label>
									<div class="certification-checkboxes">
										<div class="form-check">
											<InputCheckbox id="isVerified" class="form-check-input"
														   @bind-Value="petWalkerModel.IsVerified" />
											<label class="form-check-label" for="isVerified">Verified Walker</label>
										</div>
										<div class="form-check">
											<InputCheckbox id="hasInsurance" class="form-check-input"
														   @bind-Value="petWalkerModel.HasInsurance" />
											<label class="form-check-label" for="hasInsurance">Has Insurance</label>
										</div>
										<div class="form-check">
											<InputCheckbox id="hasFirstAid" class="form-check-input"
														   @bind-Value="petWalkerModel.HasFirstAidCertification" />
											<label class="form-check-label" for="hasFirstAid">First Aid Certified</label>
										</div>
									</div>
								</div>
							</div>

							<div class="form-section">
								<h4>Location Information</h4>
								<div class="form-group">
									<label for="city">City</label>
									<InputText id="city" class="form-control" @bind-Value="petWalkerModel.City" />
									<ValidationMessage For="@(() => petWalkerModel.City)" />
								</div>

								<div class="form-group">
									<label for="state">State</label>
									<InputText id="state" class="form-control" @bind-Value="petWalkerModel.State" />
									<ValidationMessage For="@(() => petWalkerModel.State)" />
								</div>

								<div class="form-group">
									<label for="zipCode">Zip Code</label>
									<InputText id="zipCode" class="form-control" @bind-Value="petWalkerModel.ZipCode" />
									<ValidationMessage For="@(() => petWalkerModel.ZipCode)" />
								</div>

								<div class="form-group">
									<label for="country">Country</label>
									<InputText id="country" class="form-control" @bind-Value="petWalkerModel.Country" />
									<ValidationMessage For="@(() => petWalkerModel.Country)" />
								</div>
							</div>
						</div>
						<div class="form-sections form-sections-service-areas">
						@* Service Areas *@
						<div class="form-section">
							<h4>Service Areas</h4>

							@* Structured Service Areas *@
							<div class="form-group mb-4">
								<h5>Add Service Area</h5>
								<div class="row mb-2">
									<div class="col-md-5">
										<label for="region">Region</label>
										<select id="region" class="form-control" value="@selectedRegionId" @onchange="OnRegionChanged">
											<option value="">Select Region</option>
											@foreach (var region in regions)
											{
												<option value="@region.Id">@region.RegionName</option>
											}
										</select>
									</div>
									<div class="col-md-5">
										<label for="locality">Locality</label>
										<select id="locality" class="form-control" value="@selectedLocalityId" @onchange="OnLocalityChanged"
												disabled="@(selectedRegionId == Guid.Empty)">
											<option value="">Select Locality</option>
											@foreach (var locality in localities)
											{
												<option value="@locality.Id">@locality.LocalityName</option>
											}
										</select>
									</div>
									<div class="col-md-2 d-flex align-items-end">
										<button type="button" class="btn btn-primary w-100" @onclick="AddStructuredServiceArea"
												disabled="@(selectedRegionId == Guid.Empty || selectedLocalityId == Guid.Empty)">
											Add
										</button>
									</div>
								</div>
							</div>

							@* Display Selected Service Areas *@
							<div class="form-group">
								<h5>Selected Service Areas</h5>
								@if (petWalkerModel.StructuredServiceAreas.Count > 0)
								{
									<div class="service-areas-list">
										@foreach (var area in petWalkerModel.StructuredServiceAreas)
										{
											<div class="service-area-tag @(area.RegionId == Guid.Empty ? "service-area-tag-legacy" : "")">
												@if (area.RegionId != Guid.Empty)
												{
													<span>@area.RegionName - @area.LocalityName</span>
												}
												else
												{
													<span>@area.LocalityName</span>
													<span class="legacy-indicator" title="This service area needs to be updated to use the new region-locality structure">*</span>
												}
												<button type="button" class="remove-button"
														@onclick="() => RemoveStructuredServiceArea(area.Id)"
														title="Remove this service area">
													×
												</button>
											</div>
										}
									</div>
								}
								else
								{
									<p class="text-muted">No service areas selected. Please add at least one service area.</p>
								}
							</div>

							@* Current Schedule (Read-only) *@
							<div class="form-group">
								<h5>Current Schedule</h5>
								@if (petWalkerModel.Schedules != null && petWalkerModel.Schedules.Any())
								{
									<div class="schedule-display">
										@foreach (var schedule in petWalkerModel.Schedules.OrderBy(s => (int)s.DayOfWeek == 0 ? 7 : (int)s.DayOfWeek))
										{
											<div class="schedule-day-item">
												<div class="schedule-day-name">@ScheduleHelper.GetDayDisplayName(schedule.DayOfWeek)</div>
												<div class="schedule-time-range">@schedule.TimeRangeFormatted</div>
											</div>
										}
									</div>
									<small class="text-muted">To modify the schedule, use the "Manage Schedule" button from the Pet Walker list.</small>
								}
								else
								{
									<p class="text-muted">No schedule set. Use the "Manage Schedule" button from the Pet Walker list to set availability.</p>
								}
							</div>

							@* Legacy Service Areas (Hidden) *@
							<div class="d-none">
								@for (int i = 0; i < petWalkerModel.ServiceAreas.Count; i++)
								{
									var index = i;
									<InputText @bind-Value="petWalkerModel.ServiceAreas[index]" />
								}
							</div>
						</div>
						</div>
						<div class="modal-footer">
							<div class="d-flex align-items-center justify-content-between w-100">
								<div class="success-message @(showSuccessMessage ? "show" : "")">
									<i class="fas fa-check-circle"></i> Pet walker updated successfully!
								</div>
								<div>
									<button type="button" class="btn btn-secondary me-2" @onclick="OnCancel">Cancel</button>
									<button type="submit" class="btn btn-primary">Save Changes</button>
								</div>
							</div>
						</div>
					</EditForm>
				}
			</div>
		</div>
	</div>
</div>
