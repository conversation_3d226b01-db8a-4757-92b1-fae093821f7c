@using FurryFriends.BlazorUI.Client.Pages.PetWalkers
@using FurryFriends.BlazorUI.Client.Services.Interfaces
@rendermode InteractiveServer
@implements IDisposable
@inject IPopupService PopupService
@inject ILogger<ManageSchedulePopupManager> Logger

@if (showPopup && currentPetWalkerId != Guid.Empty)
{
    <ManageSchedulePopup 
        PetWalkerId="@currentPetWalkerId" 
        OnSave="HandleSave" 
        OnCancel="HandleCancel" />
}

@code {
    private bool showPopup = false;
    private Guid currentPetWalkerId = Guid.Empty;

    protected override void OnInitialized()
    {
        SubscribeToEvents();

        // Check if the popup should be open based on the service state
        if (PopupService.IsManageSchedulePopupOpen())
        {
            currentPetWalkerId = PopupService.GetCurrentSchedulePetWalkerId();
            showPopup = true;
        }
    }

    protected override void OnAfterRender(bool firstRender)
    {
        if (firstRender)
        {
            // Ensure we're subscribed after the component is rendered
            SubscribeToEvents();

            // Check if the popup should be open based on the service state
            if (PopupService.IsManageSchedulePopupOpen() && !showPopup)
            {
                currentPetWalkerId = PopupService.GetCurrentSchedulePetWalkerId();
                showPopup = true;
                StateHasChanged();
            }
        }
    }

    private void SubscribeToEvents()
    {
        try
        {
            // Unsubscribe first to avoid duplicate subscriptions
            PopupService.OnShowManageSchedulePopup -= ShowPopup;
            PopupService.OnCloseManageSchedulePopup -= ClosePopup;

            // Subscribe to events
            PopupService.OnShowManageSchedulePopup += ShowPopup;
            PopupService.OnCloseManageSchedulePopup += ClosePopup;

            Logger.LogDebug("Successfully subscribed to schedule popup events");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error subscribing to schedule popup events");
        }
    }

    private void ShowPopup(Guid petWalkerId)
    {
        try
        {
            Logger.LogDebug("Showing schedule popup for PetWalker: {PetWalkerId}", petWalkerId);
            currentPetWalkerId = petWalkerId;
            showPopup = true;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error showing schedule popup for PetWalker: {PetWalkerId}", petWalkerId);
        }
    }

    private void ClosePopup()
    {
        try
        {
            Logger.LogDebug("Closing schedule popup");
            showPopup = false;
            currentPetWalkerId = Guid.Empty;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error closing schedule popup");
        }
    }

    private void HandleSave()
    {
        try
        {
            Logger.LogDebug("Schedule saved successfully, closing popup");
            showPopup = false;
            currentPetWalkerId = Guid.Empty;
            StateHasChanged();

            // Notify the popup service that the popup is closed
            PopupService.CloseManageSchedulePopup();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling schedule save");
        }
    }

    private void HandleCancel()
    {
        try
        {
            Logger.LogDebug("Schedule management cancelled, closing popup");
            showPopup = false;
            currentPetWalkerId = Guid.Empty;
            StateHasChanged();

            // Notify the popup service that the popup is closed
            PopupService.CloseManageSchedulePopup();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling schedule cancel");
        }
    }

    public void Dispose()
    {
        try
        {
            // Unsubscribe from events to prevent memory leaks
            PopupService.OnShowManageSchedulePopup -= ShowPopup;
            PopupService.OnCloseManageSchedulePopup -= ClosePopup;
            Logger.LogDebug("Successfully unsubscribed from schedule popup events");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error disposing schedule popup manager");
        }
    }
}
