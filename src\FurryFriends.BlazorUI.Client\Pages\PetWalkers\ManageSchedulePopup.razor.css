/* Schedule Popup Specific Styles */
.schedule-popup {
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
}

.schedule-content {
    padding: 20px;
}

.schedule-instructions {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}

.schedule-instructions p {
    margin: 0;
    color: #495057;
    font-size: 14px;
}

/* Schedule Grid */
.schedule-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.schedule-day-row {
    display: flex;
    flex-direction: column;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: #fff;
    transition: all 0.2s ease;
}

.schedule-day-row:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Day Header */
.day-header {
    margin-bottom: 10px;
}

.day-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
    font-size: 16px;
}

.day-checkbox input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.day-name {
    color: #495057;
}

/* Time Controls */
.time-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
    transition: opacity 0.2s ease;
}

.time-controls.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.time-input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.time-input-group label {
    font-size: 12px;
    font-weight: 500;
    color: #6c757d;
    text-transform: uppercase;
}

.time-input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    width: 120px;
    transition: border-color 0.2s ease;
}

.time-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.time-input:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
}

.time-separator {
    font-weight: 500;
    color: #6c757d;
    margin-top: 20px;
    font-size: 14px;
}

/* Validation */
.validation-error {
    margin-top: 8px;
}

.validation-error small {
    font-size: 12px;
}

/* Loading and Error States */
.loading-container,
.error-container {
    padding: 40px;
    text-align: center;
}

.loading-container p {
    color: #6c757d;
    font-style: italic;
}

/* Success Message */
.alert {
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 0;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Responsive Design */
@media (max-width: 768px) {
    .schedule-popup {
        width: 95%;
        margin: 10px;
    }
    
    .time-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .time-separator {
        margin-top: 0;
        align-self: center;
    }
    
    .time-input {
        width: 100%;
        max-width: 200px;
    }
}

@media (max-width: 480px) {
    .schedule-content {
        padding: 15px;
    }
    
    .schedule-day-row {
        padding: 12px;
    }
    
    .schedule-instructions {
        padding: 12px;
    }
}
