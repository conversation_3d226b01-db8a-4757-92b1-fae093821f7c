﻿public class Schedule : ValueObject
{
  public DayOfWeek DayOfWeek { get; }
  public TimeSpan StartTime { get; }
  public TimeSpan EndTime { get; }

  private Schedule() { } // EF Core needs this

  public Schedule(DayOfWeek dayOfWeek, TimeSpan startTime, TimeSpan endTime)
  {
    Guard.Against.OutOfRange((int)dayOfWeek, nameof(dayOfWeek), 0, 6);
    Guard.Against.OutOfRange(startTime, nameof(startTime), TimeSpan.Zero, TimeSpan.FromHours(23.99));
    Guard.Against.OutOfRange(endTime, nameof(endTime), TimeSpan.Zero, TimeSpan.FromHours(23.99));
    Guard.Against.InvalidInput(endTime, nameof(endTime), t => t > startTime, "End time must be after start time");

    DayOfWeek = dayOfWeek;
    StartTime = startTime;
    EndTime = endTime;
  }

  protected override IEnumerable<object> GetEqualityComponents()
  {
    yield return DayOfWeek;
    yield return StartTime;
    yield return EndTime;
  }
}
