@using FurryFriends.BlazorUI.Client.Models.PetWalkers
@using FurryFriends.BlazorUI.Client.Services.Interfaces
@using Microsoft.Extensions.Logging
@rendermode InteractiveServer

<div class="popup-overlay">
    <div class="popup-container schedule-popup">
        <div class="popup-header">
            <h3><strong>Manage Schedule</strong></h3>
            <button type="button" class="close-button" @onclick="OnCancel">×</button>
        </div>
        
        <div class="popup-content">
            @if (isLoading)
            {
                <div class="loading-container">
                    <p><em>Loading schedule...</em></p>
                </div>
            }
            else if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="error-container">
                    <div class="alert alert-danger">
                        <p>@errorMessage</p>
                        <button class="btn btn-primary mt-2" @onclick="LoadSchedule">Retry</button>
                    </div>
                </div>
            }
            else
            {
                <div class="schedule-content">
                    <div class="schedule-instructions">
                        <p>Set your availability for each day of the week. Toggle the checkbox to enable/disable a day, then set your start and end times.</p>
                    </div>
                    
                    <div class="schedule-grid">
                        @foreach (var day in ScheduleHelper.WeekDays)
                        {
                            var daySchedule = scheduleItems.FirstOrDefault(s => s.DayOfWeek == day);
                            if (daySchedule == null)
                            {
								daySchedule = new ScheduleItemDto
								{
									DayOfWeek = day,
									StartTime = TimeOnly.FromDateTime(DateTime.Parse("09:00")),
									EndTime = TimeOnly.FromDateTime(DateTime.Parse("09:00")),
									IsActive = false
								};
                                scheduleItems.Add(daySchedule);
                            }
                            
                            <div class="schedule-day-row">
                                <div class="day-header">
                                    <label class="day-checkbox">
                                        <input type="checkbox" @bind="daySchedule.IsActive" />
                                        <span class="day-name">@ScheduleHelper.GetDayDisplayName(day)</span>
                                    </label>
                                </div>

								<div class="time-controls @(daySchedule.IsActive ? "" : "disabled")">
									<div class="time-input-group">
										<label>Start Time:</label>
										<input type="time"
											   @bind="daySchedule.StartTime"
											   @bind:format="hh:mm"
											   disabled="@(!daySchedule.IsActive)"
											   class="time-input" />
									</div>

									<div class="time-separator">to</div>

									<div class="time-input-group">
										<label>End Time:</label>
										<input type="time"
											   @bind="daySchedule.EndTime"
											   @bind:format="hh:mm"
											   disabled="@(!daySchedule.IsActive)"
											   class="time-input" />
									</div>
								</div>
                                
                                @if (daySchedule.IsActive && !ScheduleHelper.IsValidScheduleItem(daySchedule))
                                {
                                    <div class="validation-error">
                                        <small class="text-danger">End time must be after start time</small>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                    
                    @if (showSuccessMessage)
                    {
                        <div class="alert alert-success mt-3">
                            <p>Schedule updated successfully!</p>
                        </div>
                    }
                </div>
            }
        </div>
        
        <div class="popup-footer">
            <button type="button" class="btn btn-secondary" @onclick="OnCancel">Cancel</button>
            <button type="button" class="btn btn-primary" @onclick="SaveSchedule" disabled="@(isLoading || isSaving)">
                @if (isSaving)
                {
                    <span>Saving...</span>
                }
                else
                {
                    <span>Save Schedule</span>
                }
            </button>
        </div>
    </div>
</div>
